# IMPROVED CRYPTOCURRENCY VISUALIZER

import ccxt
import streamlit as st
import pandas as pd
import plotly.graph_objs as go
from datetime import datetime, timedelta
import numpy as np
import time

# Check for optional dependency
try:
    from streamlit_autorefresh import st_autorefresh
    HAS_AUTOREFRESH = True
except ImportError:
    st.warning("⚠️ streamlit_autorefresh not installed. Install it with: pip install streamlit-autorefresh")
    HAS_AUTOREFRESH = False

# --- Configuration and Initialization ---
# Initialize available exchanges
available_exchanges = {
    "Binance": ccxt.binance(),
    "OKX": ccxt.okx(),
    # Add more exchanges here if needed, e.g., "Coinbase": ccxt.coinbase()
}
# Set common configuration for all exchanges
for exchange_name, exchange_obj in available_exchanges.items():
    exchange_obj.enableRateLimit = True
    # Different exchanges need different configurations
    if exchange_name == "OKX":
        # OKX works best with minimal configuration
        pass  # Use default options
    else:
        exchange_obj.options = {
            'defaultType': 'spot',
            'adjustForTimeDifference': True,
        }
# Streamlit UI Setup
st.set_page_config(layout="wide", page_title="Crypto Visualizer")
st.title("Advanced Cryptocurrency Trend Visualizer")

# --- Sidebar for Settings ---
st.sidebar.title("Settings")

# --- Helper Functions ---

def timeframe_to_milliseconds(tf):
    """Convert timeframe string to milliseconds with error handling"""
    try:
        unit = tf[-1]
        value = int(tf[:-1])
        multipliers = {'m': 60 * 1000, 'h': 60 * 60 * 1000, 'd': 24 * 60 * 60 * 1000, 'w': 7 * 24 * 60 * 60 * 1000}
        return value * multipliers.get(unit, 60 * 1000)  # Default to 1 minute
    except (ValueError, IndexError):
        return 60 * 1000  # Default to 1 minute

@st.cache_data(ttl=3600) # Cache for 1 hour
def get_available_symbols(_exchange_obj):
    """Fetch all available trading symbols for a given exchange."""
    try:
        markets = _exchange_obj.load_markets()

        # First try: active spot markets
        symbols = [s for s, market in markets.items()
                  if market.get('spot', False) and market.get('active', False)]

        # If no symbols found, try just spot markets (some exchanges might not set active flag properly)
        if not symbols:
            symbols = [s for s, market in markets.items() if market.get('spot', False)]

        # If still no symbols, try all markets (fallback)
        if not symbols:
            symbols = list(markets.keys())

        # Prioritize common quote currencies (handle both / and - separators)
        if symbols:
            priority_quotes = ['USDT', 'USDC', 'BTC', 'ETH']
            priority_symbols = []
            for s in symbols:
                # Check for both formats: BTC/USDT and BTC-USDT
                if any(s.endswith(f'/{quote}') or s.endswith(f'-{quote}') for quote in priority_quotes):
                    priority_symbols.append(s)

            other_symbols = [s for s in symbols if s not in priority_symbols]
            return sorted(priority_symbols) + sorted(other_symbols)

        return sorted(symbols)
    except Exception as e:
        st.error(f"Error loading markets from {_exchange_obj.id}: {e}")
        return []

@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_validated_symbols(_exchange_obj, max_symbols=50):
    """Get symbols that are actually available for price fetching"""
    try:
        markets = _exchange_obj.load_markets()

        # Get all potential symbols
        all_symbols = []
        for s, market in markets.items():
            if market.get('spot', False) and market.get('active', False):
                all_symbols.append(s)

        # Prioritize USDT pairs
        usdt_symbols = [s for s in all_symbols if 'USDT' in s]
        other_symbols = [s for s in all_symbols if s not in usdt_symbols]

        # Test symbols to see which ones actually work
        validated_symbols = []
        test_symbols = (usdt_symbols[:30] + other_symbols[:20])[:max_symbols]

        for symbol in test_symbols:
            try:
                # Quick test to see if ticker is available
                ticker = _exchange_obj.fetch_ticker(symbol)
                if ticker.get('last') is not None:
                    validated_symbols.append(symbol)
                    if len(validated_symbols) >= max_symbols:
                        break
            except:
                continue  # Skip symbols that don't work

        return validated_symbols
    except Exception as e:
        st.error(f"Error validating symbols for {_exchange_obj.id}: {e}")
        return []

def validate_symbol(_exchange_obj, symbol):
    """Validate if a symbol can be used for price fetching"""
    try:
        # Try to fetch ticker to validate the symbol
        ticker = _exchange_obj.fetch_ticker(symbol)
        return ticker.get('last') is not None
    except:
        return False

# Dynamic Exchange Selection
selected_exchange_name = st.sidebar.selectbox("Select Exchange", list(available_exchanges.keys()), index=0)
exchange = available_exchanges[selected_exchange_name]

# Get available symbols for selected exchange
with st.spinner(f"Loading available trading pairs from {selected_exchange_name}..."):
    available_symbols = get_available_symbols(exchange)

# Also get validated symbols for better reliability (cached)
if available_symbols:
    with st.spinner("Validating symbol availability..."):
        validated_symbols = get_validated_symbols(exchange, max_symbols=30)

if not available_symbols:
    st.error("No trading pairs available for the selected exchange. Please try another exchange.")
    st.stop()

# Symbol selection options
symbol_source = st.sidebar.radio(
    "Symbol Source",
    ["All Available", "Validated Only"],
    help="Validated symbols are tested for price fetching reliability"
)

if symbol_source == "Validated Only" and 'validated_symbols' in locals() and validated_symbols:
    symbol_list = validated_symbols
    st.sidebar.success(f"Using {len(symbol_list)} validated symbols")
else:
    symbol_list = available_symbols
    if symbol_source == "Validated Only":
        st.sidebar.warning("Validated symbols not available, using all symbols")

# Select cryptocurrencies with dynamic list - prioritize USDT pairs
usdt_symbols = [s for s in symbol_list if 'USDT' in s]

# Handle different exchange formats (BTC/USDT vs BTC-USDT)
btc_usdt_candidates = [s for s in usdt_symbols if 'BTC' in s and 'USDT' in s]
eth_usdt_candidates = [s for s in usdt_symbols if 'ETH' in s and 'USDT' in s]

if btc_usdt_candidates and eth_usdt_candidates:
    default_symbols = [btc_usdt_candidates[0], eth_usdt_candidates[0]]
elif btc_usdt_candidates:
    default_symbols = [btc_usdt_candidates[0]]
elif len(usdt_symbols) >= 2:
    default_symbols = usdt_symbols[:2]
elif len(usdt_symbols) == 1:
    default_symbols = [usdt_symbols[0]]
elif len(symbol_list) >= 2:
    default_symbols = symbol_list[:2]
elif len(symbol_list) == 1:
    default_symbols = [symbol_list[0]]
else:
    default_symbols = []
crypto_symbols = st.sidebar.multiselect(
    "Select Cryptocurrencies",
    symbol_list,
    default=default_symbols
)

if not crypto_symbols:
    st.sidebar.warning("Please select at least one cryptocurrency")
    st.stop()

# Show helpful info about symbol validation
if symbol_source == "All Available":
    st.sidebar.info("💡 Tip: Switch to 'Validated Only' to avoid symbols with fetching issues")

# Select timeframe
timeframe = st.sidebar.selectbox("Select Timeframe", ['1m', '5m', '15m', '30m', '1h', '4h', '1d'], index=6)

# Date range selection
start_date = st.sidebar.date_input("Start Date", value=pd.to_datetime('2023-01-01'))
end_date = st.sidebar.date_input("End Date", value=pd.to_datetime('today'))

if start_date >= end_date:
    st.sidebar.error("Start date must be before end date")
    st.stop()

# Refresh interval
if HAS_AUTOREFRESH:
    interval = st.sidebar.slider("Refresh Interval (seconds)", min_value=10, max_value=300, value=60)
else:
    interval = 60  # Default interval even when autorefresh is not available
    st.sidebar.info("Auto-refresh disabled (missing dependency)")

# Start and Stop buttons
col_start, col_stop = st.sidebar.columns(2)
start_button = col_start.button("Start Monitoring")
stop_button = col_stop.button("Stop Monitoring")



@st.cache_data(ttl=60)
def fetch_historical_data(_exchange_obj, symbol, timeframe_str, since_date, end_date_obj):
    """Fetch historical data with improved error handling"""
    try:
        all_ohlcv = []
        since_timestamp = int(pd.to_datetime(since_date).timestamp() * 1000)
        end_timestamp = int(pd.to_datetime(end_date_obj).timestamp() * 1000)
        timeframe_ms = timeframe_to_milliseconds(timeframe_str)
        
        # Prevent infinite loops
        max_iterations = 1000
        iteration_count = 0
        
        while iteration_count < max_iterations:
            iteration_count += 1
            
            try:
                ohlcv = _exchange_obj.fetch_ohlcv(symbol, timeframe=timeframe_str, since=since_timestamp, limit=1000)
                if not ohlcv:
                    break
                    
                all_ohlcv.extend(ohlcv)
                last_timestamp = ohlcv[-1][0]
                since_timestamp = last_timestamp + timeframe_ms
                
                if since_timestamp >= end_timestamp or len(ohlcv) < 1000:
                    break
                    
                # Add small delay to avoid rate limiting
                time.sleep(0.1)
                
            except Exception as api_error:
                st.warning(f"API error for {symbol}: {str(api_error)[:100]}...")
                break
        
        if not all_ohlcv:
            return pd.DataFrame()
            
        df = pd.DataFrame(all_ohlcv, columns=['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume'])
        df.drop_duplicates(subset='Timestamp', inplace=True)
        df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='ms')
        
        # Filter by date range
        mask = (df['Timestamp'] >= pd.to_datetime(since_date)) & (df['Timestamp'] <= pd.to_datetime(end_date_obj))
        df = df.loc[mask]
        df.sort_values('Timestamp', inplace=True)
        df.reset_index(drop=True, inplace=True)
        
        return df
        
    except Exception as e:
        st.error(f"Error fetching historical data for {symbol} on {_exchange_obj.id}: {e}")
        return pd.DataFrame()

def fetch_current_price(_exchange_obj, symbol):
    """Fetch current price with error handling"""
    try:
        ticker = _exchange_obj.fetch_ticker(symbol)
        return ticker.get('last', None)
    except Exception as e:
        # Only show warning for user-selected symbols, not for screener
        if hasattr(st.session_state, 'show_price_warnings') and st.session_state.show_price_warnings:
            # Only show warning if it's not a "does not have market symbol" error
            error_msg = str(e).lower()
            if "does not have market symbol" not in error_msg:
                st.warning(f"Error fetching current price for {symbol}: {str(e)[:100]}...")
        return None



# --- Technical Indicator Calculations ---

def calculate_rsi(series, period=14):
    """Calculate RSI with improved error handling"""
    if len(series) < period:
        return pd.Series([50] * len(series), index=series.index)
        
    delta = series.diff(1)
    gain = delta.where(delta > 0, 0.0)
    loss = -delta.where(delta < 0, 0.0)
    
    avg_gain = gain.rolling(window=period, min_periods=period).mean()
    avg_loss = loss.rolling(window=period, min_periods=period).mean()
    
    # Avoid division by zero
    avg_loss = avg_loss.replace(0, 1e-10)
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi.fillna(50)

def calculate_macd(series, fast_period=12, slow_period=26, signal_period=9):
    """Calculate MACD with validation"""
    if len(series) < slow_period:
        return pd.Series([0] * len(series), index=series.index), pd.Series([0] * len(series), index=series.index), pd.Series([0] * len(series), index=series.index)
    
    ema_fast = series.ewm(span=fast_period, adjust=False).mean()
    ema_slow = series.ewm(span=slow_period, adjust=False).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram

def calculate_bollinger_bands(series, window=20, num_std_dev=2):
    """Calculate Bollinger Bands with validation"""
    if len(series) < window:
        return series, series, series  # Return the series itself if insufficient data
        
    sma = series.rolling(window=window).mean()
    std_dev = series.rolling(window=window).std()
    upper_band = sma + (std_dev * num_std_dev)
    lower_band = sma - (std_dev * num_std_dev)
    return upper_band, sma, lower_band

def calculate_stochastic_oscillator(df, k_period=14, d_period=3):
    """Calculate Stochastic Oscillator with validation"""
    if len(df) < k_period:
        return pd.Series([50] * len(df), index=df.index), pd.Series([50] * len(df), index=df.index)
        
    lowest_low = df['Low'].rolling(window=k_period).min()
    highest_high = df['High'].rolling(window=k_period).max()
    
    # Avoid division by zero
    denominator = highest_high - lowest_low
    denominator = denominator.replace(0, 1e-10)
    
    k_line = ((df['Close'] - lowest_low) / denominator) * 100
    d_line = k_line.rolling(window=d_period).mean()
    return k_line.fillna(50), d_line.fillna(50)

def calculate_ichimoku(df, conversion_period=9, base_period=26, leading_span_b_period=52, lagging_span_period=26):
    """Calculate Ichimoku Cloud with validation"""
    if len(df) < leading_span_b_period:
        # Return empty series if insufficient data
        empty_series = pd.Series([np.nan] * len(df), index=df.index)
        return empty_series, empty_series, empty_series, empty_series, empty_series
    
    high = df['High']
    low = df['Low']
    close = df['Close']

    tenkan_sen = (high.rolling(window=conversion_period).max() + low.rolling(window=conversion_period).min()) / 2
    kijun_sen = (high.rolling(window=base_period).max() + low.rolling(window=base_period).min()) / 2
    senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(base_period)
    senkou_span_b = ((high.rolling(window=leading_span_b_period).max() + low.rolling(window=leading_span_b_period).min()) / 2).shift(base_period)
    chikou_span = close.shift(-base_period)

    return tenkan_sen, kijun_sen, senkou_span_a, senkou_span_b, chikou_span

def calculate_fibonacci_retracements(df):
    """Calculate Fibonacci retracements with validation"""
    if df.empty or len(df) < 2:
        return {}
    
    price_min = df['Low'].min()
    price_max = df['High'].max()
    
    if price_max == price_min:
        return {'50%': price_max}
    
    diff = price_max - price_min
    
    levels = {
        '0%': price_max,
        '23.6%': price_max - (0.236 * diff),
        '38.2%': price_max - (0.382 * diff),
        '50%': price_max - (0.500 * diff),
        '61.8%': price_max - (0.618 * diff),
        '78.6%': price_max - (0.786 * diff),
        '100%': price_min
    }
    return levels

def determine_trend(rsi):
    """Determine trend based on RSI"""
    if pd.isna(rsi):
        return "Hold"
    if rsi < 30:
        return "Buy"
    elif rsi > 70:
        return "Sell"
    else:
        return "Hold"

def detect_price_anomaly(df_close, threshold_std_dev=3):
    """Detect price anomalies with improved validation"""
    if len(df_close) < 5:  # Need more data points
        return None
    
    daily_returns = df_close.pct_change().dropna()
    if daily_returns.empty or len(daily_returns) < 2:
        return None

    mean_return = daily_returns.mean()
    std_return = daily_returns.std()

    if std_return == 0 or pd.isna(std_return):
        return None

    last_return = daily_returns.iloc[-1]
    if pd.isna(last_return):
        return None
        
    z_score = (last_return - mean_return) / std_return

    if abs(z_score) > threshold_std_dev:
        return f"Significant price movement detected! Z-score: {z_score:.2f}"
    return None

# --- Session State Management ---
if 'monitoring' not in st.session_state:
    st.session_state['monitoring'] = False
if 'active_alerts' not in st.session_state:
    st.session_state['active_alerts'] = []
if 'show_price_warnings' not in st.session_state:
    st.session_state['show_price_warnings'] = True

if start_button:
    st.session_state['monitoring'] = True
if stop_button:
    st.session_state['monitoring'] = False

# --- Main Application Logic ---
if st.session_state['monitoring']:
    # Auto-refresh if available
    if HAS_AUTOREFRESH:
        st_autorefresh(interval=interval * 1000, key="crypto_autorefresh")

    # Current Prices Display
    st.subheader("Current Prices and Trends")

    # Limit concurrent API calls
    if len(crypto_symbols) > 5:
        st.info(f"Monitoring {len(crypto_symbols)} symbols. Consider selecting fewer symbols for better performance.")

    cols = st.columns(min(len(crypto_symbols), 4))  # Limit columns for better layout

    # Enable warnings for user-selected symbols
    st.session_state.show_price_warnings = True

    for i, symbol in enumerate(crypto_symbols[:4]):  # Limit to first 4 for display
        with cols[i % len(cols)]:
            current_price = fetch_current_price(exchange, symbol)
            if current_price is not None:
                df_recent = fetch_historical_data(exchange, symbol, '1d', pd.to_datetime('now') - timedelta(days=30), pd.to_datetime('now'))
                
                trend = "N/A"
                anomaly_alert = None
                
                if not df_recent.empty:
                    if len(df_recent) >= 14:
                        df_recent['RSI'] = calculate_rsi(df_recent['Close'], period=14)
                        current_rsi = df_recent['RSI'].iloc[-1]
                        trend = determine_trend(current_rsi)
                    
                    anomaly_alert = detect_price_anomaly(df_recent['Close'])

                st.markdown(f"""
                    <div style='text-align: center; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>
                        <h4>{symbol} ({exchange.id.upper()})</h4>
                        <p style='font-size: 1.5em; font-weight: bold; color: #2e8b57;'>${current_price:,.2f}</p>
                        <p style='font-size: 1.2em;'>Trend: {trend}</p>
                    </div>
                """, unsafe_allow_html=True)
                
                if anomaly_alert:
                    st.warning(f"⚠️ **{symbol}:** {anomaly_alert}")
            else:
                st.error(f"❌ {symbol}: Price unavailable")

    if len(crypto_symbols) > 4:
        st.info(f"Showing first 4 symbols. {len(crypto_symbols) - 4} more selected symbols available in detailed charts below.")

    st.markdown("<hr>", unsafe_allow_html=True)

    # --- Alert System (Simplified) ---
    with st.sidebar.expander("🔔 Smart Alerts"):
        st.write("Set price alerts:")
        
        if crypto_symbols:
            alert_symbol = st.selectbox("Symbol", crypto_symbols, key="alert_symbol")
            alert_price = st.number_input("Price Threshold", min_value=0.0, value=0.0, key="alert_price")
            alert_condition = st.selectbox("Condition", ["Above", "Below"], key="alert_condition")
            
            if st.button("Add Alert", key="add_alert"):
                new_alert = {
                    "symbol": alert_symbol,
                    "price": alert_price,
                    "condition": alert_condition,
                    "triggered": False
                }
                st.session_state['active_alerts'].append(new_alert)
                st.success(f"Alert added for {alert_symbol}!")

        # Display active alerts
        if st.session_state['active_alerts']:
            st.write("**Active Alerts:**")
            for i, alert in enumerate(st.session_state['active_alerts']):
                st.write(f"• {alert['symbol']}: {alert['condition']} ${alert['price']:,.2f}")
            
            if st.button("Clear All Alerts", key="clear_alerts"):
                st.session_state['active_alerts'] = []
                st.success("All alerts cleared!")

    # --- Tabbed Interface ---
    tab1, tab2, tab3 = st.tabs(["📊 Charts", "📈 Indicators", "🔍 Screener"])

    with tab1:
        st.subheader("Price Charts")
        
        for symbol in crypto_symbols:
            with st.expander(f"{symbol} Chart", expanded=len(crypto_symbols) == 1):
                df = fetch_historical_data(exchange, symbol, timeframe, start_date, end_date)
                
                if df.empty:
                    st.warning(f"No data available for {symbol}")
                    continue

                # Main price chart
                fig = go.Figure()
                fig.add_trace(go.Candlestick(
                    x=df['Timestamp'],
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name='Price'
                ))

                # Add Fibonacci levels
                fib_levels = calculate_fibonacci_retracements(df)
                for level_name, value in fib_levels.items():
                    fig.add_hline(
                        y=value, 
                        line_dash="dash", 
                        line_color="gray",
                        annotation_text=f"Fib {level_name}: ${value:,.2f}",
                        annotation_position="top left"
                    )

                fig.update_layout(
                    title=f"{symbol} Price Chart ({exchange.id.upper()})",
                    xaxis_title="Time",
                    yaxis_title="Price (USDT)",
                    template="plotly_white",
                    height=500
                )
                
                st.plotly_chart(fig, use_container_width=True)

                # Download data option
                if not df.empty:
                    csv = df.to_csv(index=False)
                    st.download_button(
                        label=f"📥 Download {symbol} Data",
                        data=csv,
                        file_name=f'{symbol.replace("/", "_")}_data.csv',
                        mime='text/csv',
                        key=f"download_{symbol}"
                    )

    with tab2:
        st.subheader("Technical Indicators")
        
        for symbol in crypto_symbols:
            with st.expander(f"{symbol} Indicators", expanded=len(crypto_symbols) == 1):
                df = fetch_historical_data(exchange, symbol, timeframe, start_date, end_date)
                
                if df.empty:
                    st.warning(f"No data available for {symbol}")
                    continue

                # Calculate indicators
                if len(df) >= 20:  # Minimum data for most indicators
                    df['RSI'] = calculate_rsi(df['Close'])
                    df['BB_Upper'], df['BB_Middle'], df['BB_Lower'] = calculate_bollinger_bands(df['Close'])
                    
                    # RSI Chart
                    fig_rsi = go.Figure()
                    fig_rsi.add_trace(go.Scatter(x=df['Timestamp'], y=df['RSI'], name='RSI', line=dict(color='purple')))
                    fig_rsi.add_hline(y=70, line_dash="dash", line_color="red", annotation_text="Overbought")
                    fig_rsi.add_hline(y=30, line_dash="dash", line_color="green", annotation_text="Oversold")
                    fig_rsi.update_layout(
                        title=f"{symbol} RSI",
                        yaxis=dict(range=[0, 100]),
                        template="plotly_white",
                        height=300
                    )
                    st.plotly_chart(fig_rsi, use_container_width=True)

                    # Bollinger Bands
                    fig_bb = go.Figure()
                    fig_bb.add_trace(go.Scatter(x=df['Timestamp'], y=df['Close'], name='Close', line=dict(color='black')))
                    fig_bb.add_trace(go.Scatter(x=df['Timestamp'], y=df['BB_Upper'], name='Upper Band', line=dict(color='red')))
                    fig_bb.add_trace(go.Scatter(x=df['Timestamp'], y=df['BB_Lower'], name='Lower Band', line=dict(color='green')))
                    fig_bb.update_layout(
                        title=f"{symbol} Bollinger Bands",
                        template="plotly_white",
                        height=400
                    )
                    st.plotly_chart(fig_bb, use_container_width=True)
                else:
                    st.warning(f"Insufficient data for technical indicators ({len(df)} data points)")

    with tab3:
        st.subheader("Market Screener")

        screener_data = []
        progress_bar = st.progress(0)

        # Disable warnings for screener
        st.session_state.show_price_warnings = False

        # Use validated symbols if available, otherwise fall back to filtered symbols
        if 'validated_symbols' in locals() and validated_symbols:
            screener_symbols = validated_symbols[:15]
            st.info(f"Using {len(screener_symbols)} pre-validated symbols for reliable data")
        else:
            # Fallback to filtered symbols
            popular_symbols = [s for s in available_symbols if 'USDT' in s][:15]
            if not popular_symbols:
                popular_symbols = available_symbols[:10]
            screener_symbols = popular_symbols

        successful_fetches = 0
        for i, symbol in enumerate(screener_symbols):
            progress_bar.progress((i + 1) / len(screener_symbols))

            try:
                df = fetch_historical_data(exchange, symbol, '1d', pd.to_datetime('now') - timedelta(days=2), pd.to_datetime('now'))

                if not df.empty and len(df) >= 1:
                    current_price = df['Close'].iloc[-1]
                    change_pct = 0.0

                    if len(df) >= 2:
                        previous_price = df['Close'].iloc[-2]
                        change_pct = ((current_price - previous_price) / previous_price) * 100

                    screener_data.append({
                        'Symbol': symbol,
                        'Price': current_price,
                        '24h Change (%)': change_pct,
                        'Status': '🔥' if change_pct > 5 else '❄️' if change_pct < -5 else '➡️'
                    })
                    successful_fetches += 1

                    # Stop after getting 10 successful results
                    if successful_fetches >= 10:
                        break
            except Exception:
                # Silently skip problematic symbols
                continue
        
        progress_bar.empty()
        
        if screener_data:
            screener_df = pd.DataFrame(screener_data)
            screener_df['Price'] = screener_df['Price'].apply(lambda x: f"${x:,.2f}")
            screener_df['24h Change (%)'] = screener_df['24h Change (%)'].apply(lambda x: f"{x:.2f}%")
            
            st.dataframe(screener_df, use_container_width=True)
        else:
            st.warning("No screener data available")

    # Status footer
    st.markdown("---")
    col1, col2 = st.columns(2)
    with col1:
        st.success(f"✅ Monitoring {len(crypto_symbols)} symbols on {exchange.id.upper()}")
    with col2:
        st.info(f"🕒 Last updated: {datetime.now().strftime('%H:%M:%S')}")

else:
    st.info("👆 Click **Start Monitoring** in the sidebar to begin!")
    st.markdown("""
    ### Features:
    - 📊 Real-time price monitoring
    - 📈 Technical indicators (RSI, MACD, Bollinger Bands)
    - 🔔 Smart price alerts
    - 📥 Data export capabilities
    - 🔍 Market screening tools
    
    ### Getting Started:
    1. Select an exchange from the sidebar
    2. Choose cryptocurrencies to monitor
    3. Set your preferred timeframe
    4. Click "Start Monitoring"
    """)
