# Cryptocurrency Dashboard

An advanced cryptocurrency trend visualizer built with Streamlit that provides real-time price monitoring, technical indicators, and market screening tools.

## Features

- 📊 Real-time price monitoring from multiple exchanges (Binance, OKX)
- 📈 Technical indicators (RSI, MACD, Bollinger Bands, Ichimoku Cloud)
- 🔔 Smart price alerts
- 📥 Data export capabilities
- 🔍 Market screening tools
- 🎯 Fibonacci retracement levels
- ⚡ Auto-refresh functionality

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Run the application:
```bash
streamlit run crypto_app.py
```

## Usage

1. Select an exchange from the sidebar
2. Choose symbol source:
   - **All Available**: Shows all trading pairs from the exchange
   - **Validated Only**: Shows only pre-tested symbols that work reliably
3. Choose cryptocurrencies to monitor
4. Set your preferred timeframe and date range
5. Click "Start Monitoring" to begin real-time tracking

### Symbol Validation Feature

The app now includes intelligent symbol validation:
- **Automatic Testing**: Symbols are pre-tested for price fetching reliability
- **Error Prevention**: Reduces "does not have market symbol" errors
- **User Choice**: Option to use all symbols or only validated ones
- **Smart Filtering**: Prioritizes USDT pairs and popular cryptocurrencies

## Dependencies

- streamlit>=1.28.0
- ccxt>=4.0.0
- pandas>=1.5.0
- plotly>=5.15.0
- numpy>=1.24.0
- streamlit-autorefresh>=0.0.1 (optional, for auto-refresh)

## Fixed Issues

The following issues have been resolved:
- ✅ Duplicate comment removed
- ✅ Function definition order corrected
- ✅ Variable naming consistency fixed
- ✅ Undefined variable references resolved
- ✅ Improved error handling for edge cases
- ✅ Better default symbol selection logic
- ✅ Fixed "does not have market symbol" errors
- ✅ Improved symbol filtering for better reliability
- ✅ Enhanced Market Screener with better error handling
- ✅ Prioritized USDT pairs for more stable data
- ✅ Reduced verbose error messages in screener
- ✅ **MAJOR FIX**: Resolved "No trading pairs available" error for both exchanges
- ✅ Fixed OKX exchange configuration issues
- ✅ Added support for different symbol formats (BTC/USDT vs BTC-USDT)
- ✅ Optimized exchange-specific configurations
- ✅ **NEW**: Added symbol validation system to prevent "does not have market symbol" errors
- ✅ **NEW**: Smart symbol filtering with pre-validation
- ✅ **NEW**: User option to choose between "All Available" vs "Validated Only" symbols
- ✅ Improved error handling to suppress irrelevant warnings

## Note

Make sure you have a stable internet connection as the app fetches real-time data from cryptocurrency exchanges.
